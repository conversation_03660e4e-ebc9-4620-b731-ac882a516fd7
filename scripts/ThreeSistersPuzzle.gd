extends Control
class_name ThreeSistersPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var statements_label: RichTextLabel = $PuzzlePanel/VBoxContainer/StatementsLabel
@onready var clue_label: Label = $PuzzlePanel/VBoxContainer/ClueLabel
@onready var maria_button: TextureButton = $PuzzlePanel/VBoxContainer/PortraitsContainer/MariaButton
@onready var anna_button: TextureButton = $PuzzlePanel/VBoxContainer/PortraitsContainer/AnnaButton
@onready var isabelle_button: TextureButton = $PuzzlePanel/VBoxContainer/PortraitsContainer/IsabelleButton
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton


var correct_answer: String = "maria"  # Mária je tá, ktorá hovorí pravdu (nie je vinná)
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	# Pripojenie signálov
	print("🔗 ThreeSistersPuzzle: Connecting signals...")

	if maria_button:
		maria_button.pressed.connect(_on_maria_pressed)
	if anna_button:
		anna_button.pressed.connect(_on_anna_pressed)
	if isabelle_button:
		isabelle_button.pressed.connect(_on_isabelle_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	reset_puzzle()

func reset_puzzle():
	hint_level = 0
	reset_button_states()

func reset_button_states():
	# Nastavenie normálnych farieb
	if maria_button:
		maria_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if anna_button:
		anna_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if isabelle_button:
		isabelle_button.modulate = Color(1.0, 1.0, 1.0, 1.0)

func _on_maria_pressed():
	# Správna odpoveď - Mária hovorí pravdu
	maria_button.modulate = Color.GREEN
	AudioManager.play_puzzle_success_sound()
	show_success_feedback()
	await get_tree().create_timer(1.5).timeout
	puzzle_solved.emit()
	hide()

func _on_anna_pressed():
	# Nesprávna odpoveď
	anna_button.modulate = Color.RED
	AudioManager.play_puzzle_error_sound()
	show_error_feedback("Anna nie je správna odpoveď!")
	await get_tree().create_timer(2.0).timeout
	reset_button_states()

func _on_isabelle_pressed():
	# Nesprávna odpoveď
	isabelle_button.modulate = Color.RED
	AudioManager.play_puzzle_error_sound()
	show_error_feedback("Isabelle nie je správna odpoveď!")
	await get_tree().create_timer(2.0).timeout
	reset_button_states()

func show_success_feedback():
	# Zelené zablikanie správneho tlačidla (Mária)
	var tween = create_tween()
	tween.tween_property(maria_button, "modulate", Color.WHITE, 0.3)
	tween.tween_property(maria_button, "modulate", Color.GREEN, 0.3)
	tween.tween_property(maria_button, "modulate", Color.WHITE, 0.3)
	tween.tween_property(maria_button, "modulate", Color.GREEN, 0.3)

func show_error_feedback(message: String):
	# Zobraz chybovú správu
	print("Chyba: ", message)

func _on_hint_pressed():
	print("🔍 ThreeSistersPuzzle: Hint button pressed!")
	hint_level += 1

	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		print("🔍 ThreeSistersPuzzle: Showing hint: ", hint_text)
		show_simple_hint_dialog(hint_text)
	else:
		print("🔍 ThreeSistersPuzzle: All hints used")
		show_simple_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Ak len jedna hovorí pravdu, skúste postupne predpokladať, že pravdu hovorí každá z nich."
		2:
			return "Overte každú možnosť: vedie k logickému sporu alebo nie?"
		3:
			return "Ak Mária hovorí pravdu, Anna je nevinná a Isabelle nezradila. Mária je správna odpoveď!"
		_:
			return "Už ste použili všetky nápovedy!"

func show_simple_hint_dialog(hint_text: String):
	# Vytvorenie responzívneho hint dialógu
	var hint_overlay = ColorRect.new()
	hint_overlay.color = Color(0, 0, 0, 0.8)
	hint_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(hint_overlay)

	var hint_panel = NinePatchRect.new()
	hint_panel.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
	hint_panel.patch_margin_left = 25
	hint_panel.patch_margin_top = 25
	hint_panel.patch_margin_right = 25
	hint_panel.patch_margin_bottom = 25
	# Responzívna veľkosť - max 80% šírky obrazovky, min 300px
	var screen_size = get_viewport().get_visible_rect().size
	var panel_width = min(max(300, screen_size.x * 0.8), 500)
	var panel_height = min(max(150, screen_size.y * 0.4), 300)
	hint_panel.size = Vector2(panel_width, panel_height)

	# Manuálne centrovanie
	hint_panel.position = Vector2(
		(screen_size.x - panel_width) / 2,
		(screen_size.y - panel_height) / 2
	)
	hint_overlay.add_child(hint_panel)

	# VBoxContainer pre obsah
	var content_vbox = VBoxContainer.new()
	content_vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_vbox.add_theme_constant_override("separation", 15)
	hint_panel.add_child(content_vbox)

	# Margin container
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_left", 25)
	margin_container.add_theme_constant_override("margin_right", 25)
	margin_container.add_theme_constant_override("margin_top", 25)
	margin_container.add_theme_constant_override("margin_bottom", 25)
	content_vbox.add_child(margin_container)

	var inner_vbox = VBoxContainer.new()
	inner_vbox.add_theme_constant_override("separation", 10)
	margin_container.add_child(inner_vbox)

	# Titulok
	var title_label = Label.new()
	title_label.text = "💡 Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 20)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))
	inner_vbox.add_child(title_label)

	# Text nápovedy s automatickým zalamovaním
	var hint_label = RichTextLabel.new()
	hint_label.bbcode_enabled = true
	hint_label.text = "[center]" + hint_text + "[/center]"
	hint_label.fit_content = true
	hint_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hint_label.size_flags_vertical = Control.SIZE_EXPAND_FILL
	hint_label.custom_minimum_size = Vector2(0, 60)
	inner_vbox.add_child(hint_label)

	# Tlačidlo OK
	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	ok_button.custom_minimum_size = Vector2(100, 40)
	inner_vbox.add_child(ok_button)

	# Pripojenie signálu na zatvorenie
	ok_button.pressed.connect(func(): hint_overlay.queue_free())

	# Možnosť zatvoriť kliknutím na overlay
	hint_overlay.gui_input.connect(func(event):
		if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			hint_overlay.queue_free()
	)

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
