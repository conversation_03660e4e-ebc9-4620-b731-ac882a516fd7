extends Control

# Save Game Style Chapter Selection Menu

@onready var title_label: Label = $MainPanel/TitleLabel
@onready var chapter_title_label: Label = $MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent/ChapterTitle
@onready var chapter_description: RichTextLabel = $MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent/ChapterDescription
@onready var chapter_image: TextureRect = $MainPanel/MainContainer/ChapterImageContainer/CenterContainer/ChapterImage
@onready var play_button: TextureButton = $MainPanel/ButtonsContainer/PlayButton
@onready var back_button: TextureButton = $MainPanel/ButtonsContainer/BackButton

# Carousel elements
@onready var prev_button: TextureButton = $MainPanel/MainContainer/CarouselContainer/PrevButton
@onready var next_button: TextureButton = $MainPanel/MainContainer/CarouselContainer/NextButton
@onready var chapter_indicator: Label = $MainPanel/MainContainer/CarouselContainer/ChapterIndicator

# Button labels
@onready var play_label: Label = $MainPanel/ButtonsContainer/PlayButton/PlayLabel
@onready var back_label: Label = $MainPanel/ButtonsContainer/BackButton/BackLabel

var current_chapter: int = 1
var max_chapters: int = 7  # 7 chapters total, 7th is epilog

# Obrázky kapitol
var chapter_images = {
	1: preload("res://assets/Kapitoly_VISUALS/Kapitola_1.png"),
	2: preload("res://assets/Kapitoly_VISUALS/Kapitola_2.png"),
	3: preload("res://assets/Kapitoly_VISUALS/Kapitola_3.png"),
	4: preload("res://assets/Kapitoly_VISUALS/Kapitola_4.png"),
	5: preload("res://assets/Kapitoly_VISUALS/Kapitola_5.png"),
	6: preload("res://assets/Kapitoly_VISUALS/Kapitola_6.png"),
	7: preload("res://assets/Kapitoly_VISUALS/Kapitola_7.png"),
	8: preload("res://assets/Kapitoly_VISUALS/Kapitola_7.png")  # Použiť obrázok kapitoly 7 pre epilóg
}

# Textures for selection states
var line_current_texture = preload("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Line Select Current.png")
var line_hover_texture = preload("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Line Select Hover.png")

# Informácie o kapitolách
var chapter_info = {
	1: {
		"title": "Kapitola 1 - Cesta na zámok",
		"description": "Marec 1894. Búrlivá cesta karpatskými lesmi k Van Helsingovmu zámku. Rozlúšti šifry a nájdi cestu cez temný les."
	},
	2: {
		"title": "Kapitola 2 - Brána zámku",
		"description": "Železná brána s krvavým nápisom. Dokáž Viktorovi, že patríš k Rádu Striebornej ruže."
	},
	3: {
		"title": "Kapitola 3 - Pátranie v zámku",
		"description": "Vstupná hala a knižnica skrývajú stopy po Van Helsingovi. Objavuj tajomstvá a hľadaj cestu hlbšie."
	},
	4: {
		"title": "Kapitola 4 - Tajné krídlo",
		"description": "Staré krídlo zámku plné pascí a mechanizmov. Priprav elixír ochrany v alchymistickom laboratóriu."
	},
	5: {
		"title": "Kapitola 5 - Krypty",
		"description": "Zostup do pradávnych katakomb. Rozlúšti kódy a nájdi sarkofág grófky Isabelle."
	},
	6: {
		"title": "Kapitola 6 - Konfrontácia",
		"description": "Grófka Isabelle sa prebúdza. Vykonaj posledný rituál a zachráň Van Helsinga."
	},
	7: {
		"title": "Epilóg - Nový začiatok",
		"description": "Budapešť, jar 1894. Van Helsing sa zotavuje a tvoja cesta lovca nestvôr sa len začína."
	}
}

func _ready():
	print("🎮 NewChaptersMenu: Začínam inicializáciu...")

	# Kontrola uzlov
	print("🔍 Kontrolujem uzly...")
	print("  - title_label: ", title_label != null)
	print("  - chapter_title_label: ", chapter_title_label != null)
	print("  - chapter_description: ", chapter_description != null)
	print("  - chapter_image: ", chapter_image != null)
	print("  - play_button: ", play_button != null)
	print("  - back_button: ", back_button != null)

	# Pripojenie signálov
	print("🔗 Pripájam signály...")
	connect_signals()

	# Aplikovanie fontov
	print("🎨 Aplikujem fonty...")
	apply_fonts()

	# Aktualizovanie zobrazenia
	print("🖼️ Aktualizujem zobrazenie...")
	update_chapter_display()

	# Nastavenie fokusu
	if play_button:
		play_button.grab_focus()
		print("🎯 Fokus nastavený na play button")

	print("✅ NewChaptersMenu: Inicializácia dokončená!")

func connect_signals():
	"""Pripojenie signálov"""
	# Carousel navigácia
	if prev_button:
		prev_button.pressed.connect(_on_prev_chapter)
	if next_button:
		next_button.pressed.connect(_on_next_chapter)

	# Hlavné tlačidlá
	if play_button:
		play_button.pressed.connect(_on_play_pressed)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	print("🎨 Aplikujem fonty na NewChaptersMenu...")

	# Jednoduché nastavenie farieb bez FontLoader
	if title_label:
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		title_label.add_theme_font_size_override("font_size", 32)

	if chapter_title_label:
		chapter_title_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		chapter_title_label.add_theme_font_size_override("font_size", 20)

	if chapter_description:
		chapter_description.add_theme_color_override("default_color", Color("#E0E0E0"))
		chapter_description.add_theme_font_size_override("normal_font_size", 16)

	# Carousel labels
	if chapter_indicator:
		chapter_indicator.add_theme_color_override("font_color", Color("#D4AF37"))
		chapter_indicator.add_theme_font_size_override("font_size", 20)



	# Button labels
	if play_label:
		play_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		play_label.add_theme_font_size_override("font_size", 18)

	if back_label:
		back_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		back_label.add_theme_font_size_override("font_size", 18)

	print("✅ Fonty aplikované na NewChaptersMenu")

func update_chapter_display():
	"""Aktualizuje zobrazenie aktuálnej kapitoly"""
	if chapter_title_label and chapter_info.has(current_chapter):
		chapter_title_label.text = chapter_info[current_chapter]["title"]

	if chapter_description and chapter_info.has(current_chapter):
		var description_text = chapter_info[current_chapter]["description"]
		chapter_description.text = description_text

	# Aktualizovanie obrázka kapitoly
	if chapter_image and chapter_images.has(current_chapter):
		chapter_image.texture = chapter_images[current_chapter]

	# Aktualizovanie carousel elementov
	update_carousel_display()



func update_carousel_display():
	"""Aktualizuje carousel zobrazenie"""
	# Aktualizovanie indikátora
	if chapter_indicator:
		chapter_indicator.text = str(current_chapter) + " / " + str(max_chapters)

	# Aktualizovanie stavu kapitoly
	update_chapter_status()

	# Aktualizovanie dostupnosti navigačných tlačidiel
	if prev_button:
		prev_button.disabled = (current_chapter <= 1)
		prev_button.modulate = Color.WHITE if not prev_button.disabled else Color(0.5, 0.5, 0.5, 1.0)

	if next_button:
		next_button.disabled = (current_chapter >= max_chapters)
		next_button.modulate = Color.WHITE if not next_button.disabled else Color(0.5, 0.5, 0.5, 1.0)

func update_chapter_status():
	"""Aktualizuje stav a progress aktuálnej kapitoly - už sa nepoužíva"""
	# Funkcia vymazaná - netreba status ani progress label



func _on_prev_chapter():
	"""Navigácia na predchádzajúcu kapitolu"""
	if current_chapter > 1:
		current_chapter -= 1
		update_chapter_display()
		AudioManager.play_menu_button_sound()

func _on_next_chapter():
	"""Navigácia na nasledujúcu kapitolu"""
	if current_chapter < max_chapters:
		current_chapter += 1
		update_chapter_display()
		AudioManager.play_menu_button_sound()





func _on_play_pressed():
	print("Spúšťam kapitolu ", current_chapter)
	AudioManager.play_menu_button_sound()

	# Všetky kapitoly 1-7 sa spúšťajú normálne (7. kapitola je epilóg)
	GameManager.go_to_chapter(current_chapter)

func _on_back_pressed():
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_left"):
		_on_prev_chapter()
	elif event.is_action_pressed("ui_right"):
		_on_next_chapter()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
