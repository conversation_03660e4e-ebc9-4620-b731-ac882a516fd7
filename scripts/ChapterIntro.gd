extends Control

@onready var animated_sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var chapter_title: Label = $TextContainer/ChapterTitle
@onready var chapter_description: Label = $TextContainer/ChapterDescription
@onready var fade_overlay: ColorRect = $FadeOverlay
@onready var audio_player: AudioStreamPlayer = $AudioPlayer
@onready var background_image: TextureRect = $BackgroundImage

var current_chapter: int = 1

# Background images pre kapitoly
var chapter_backgrounds = {
	1: "res://assets/Obrázky/Kapitola_1/1.png",  # Kapitola 1 má iný priečinok
	2: "res://assets/pozadia/Kapitola_2/1.png",
	3: "res://assets/pozadia/Kapitola_3/1.png",
	4: "res://assets/pozadia/Kapitola_4/3.png",  # Kapitola 4 používa 3.png ako počiatočné
	5: "res://assets/pozadia/Kapitola_5/1.png",
	6: "res://assets/pozadia/Kapitola_6/1.png",
	7: "res://assets/pozadia/Kapitola_7/1.png"
}

# Texty pre kapitoly - presne podľa zadania
var chapter_data = {
	1: {
		"title": "KAPITOLA 1: CESTA NA ZÁMOK",
		"description": "Kočiar ťa nesie do srdca Karpát – k záhade, ktorá nedá Van Helsingovi spať.",
		"sprites_path": "res://assets/chapter_sprites/chapter_1/"
	},
	2: {
		"title": "KAPITOLA 2: BRÁNA ZÁMKU",
		"description": "Brána zámku sa otvára len tomu, kto pozná odpovede Rádu.",
		"sprites_path": "res://assets/chapter_sprites/chapter_2/"
	},
	3: {
		"title": "KAPITOLA 3: PÁTRANIE V ZÁMKU",
		"description": "Zmiznutie mentora ťa vedie hlbšie – do knižníc, denníkov a zakázaných spisov.",
		"sprites_path": "res://assets/chapter_sprites/chapter_3/"
	},
	4: {
		"title": "KAPITOLA 4: TAJNÉ KRÍDLO",
		"description": "Staré múry ukrývajú pasce, elixír i kľúč k Isabelleinej hrobke.",
		"sprites_path": "res://assets/chapter_sprites/chapter_4/"
	},
	5: {
		"title": "KAPITOLA 5: KRYPTY",
		"description": "Katakomby dýchajú smrťou – a v ich tieni číha minulosť rodu Báthoryovcov.",
		"sprites_path": "res://assets/chapter_sprites/chapter_5/"
	},
	6: {
		"title": "KAPITOLA 6: KONFRONTÁCIA",
		"description": "Grófka sa prebúdza… a ty si poslednou nádejou, že zlo znova neuspeje.",
		"sprites_path": "res://assets/chapter_sprites/chapter_6/"
	},
	7: {
		"title": "KAPITOLA 7: ZÁCHRANA MENTORA",
		"description": "Van Helsing žije – no jeho čas sa kráti. Boj ešte nie je celkom dohraný.",
		"sprites_path": "res://assets/chapter_sprites/chapter_7/"
	}
}

func _setup_mobile_layout():
	"""Nastaví responzívne rozloženie pre mobilné zariadenia"""
	var screen_size = get_viewport().get_visible_rect().size
	var screen_width = screen_size.x
	var screen_height = screen_size.y

	# Získaj TextContainer
	var text_container = $TextContainer

	if screen_width <= 480:  # Mobilné zariadenia (360-480px)
		# Užší kontajner s väčšími okrajmi pre lepšie zalamovanie
		text_container.offset_left = -260.0
		text_container.offset_right = 260.0
		text_container.offset_top = -200.0
		text_container.offset_bottom = 200.0

		# Menší spacer medzi titulkom a popisom
		var spacer = $TextContainer/Spacer
		spacer.custom_minimum_size = Vector2(0, 15)

	elif screen_width <= 768:  # Tablety
		# Stredný kontajner
		text_container.offset_left = -300.0
		text_container.offset_right = 300.0
		text_container.offset_top = -180.0
		text_container.offset_bottom = 180.0

		var spacer = $TextContainer/Spacer
		spacer.custom_minimum_size = Vector2(0, 20)
	else:  # Desktop
		# Pôvodné rozmery pre desktop
		text_container.offset_left = -300.0
		text_container.offset_right = 300.0
		text_container.offset_top = -150.0
		text_container.offset_bottom = 150.0

	# Pre všetky zariadenia - zabezpeč správne zalamovanie textu
	chapter_title.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	chapter_description.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART

	# Nastaví lepšie zalamovanie pre dlhšie texty
	chapter_title.clip_contents = true
	chapter_description.clip_contents = true

func _ready():
	# Okamžite zastav main menu hudbu pri spustení ChapterIntro
	if AudioManager and is_instance_valid(AudioManager):
		AudioManager.stop_main_menu_music_immediately()

	# Nastav responzívne rozloženie pre mobilné zariadenia
	_setup_mobile_layout()

	# Aplikuj správne fonty
	_apply_fonts()

	# Skryj text na začiatku a nastav fade overlay
	chapter_title.modulate.a = 0.0
	chapter_description.modulate.a = 0.0
	chapter_description.text = ""

	# Začni bez fade overlay - žiadna šedá obrazovka
	fade_overlay.color.a = 0.0
	fade_overlay.visible = false  # Úplne skryť overlay

	# Nastav background image pre aktuálnu kapitolu (skrytý na začiatku)
	if background_image:
		background_image.modulate.a = 0.0

	# Spustí intro pre aktuálnu kapitolu z GameManager
	if GameManager and is_instance_valid(GameManager):
		start_chapter_intro(GameManager.current_chapter)
	else:
		print("❌ GameManager nedostupný, spúšťam kapitolu 1")
		start_chapter_intro(1)

func _apply_fonts():
	"""Aplikuje správne fonty podľa dizajnu hry s mobilnou optimalizáciou"""
	# Získaj veľkosť obrazovky pre responzívne fonty
	var screen_size = get_viewport().get_visible_rect().size
	var screen_width = screen_size.x

	# Titulok kapitoly - Cinzel, zlatá farba, responzívna veľkosť
	var title_size = 48  # Mierne zmenšené z 52
	if screen_width <= 480:  # Mobilné zariadenia
		title_size = 32  # Zmenšené z 36 pre lepšie zalamovanie
	elif screen_width <= 768:  # Tablety
		title_size = 40  # Zmenšené z 44

	if FontLoader:
		FontLoader.apply_font_style(chapter_title, "chapter_title", title_size)

	# Popis kapitoly - Cormorant, krémová farba, responzívna veľkosť
	var desc_size = 26  # Mierne zmenšené z 28
	if screen_width <= 480:  # Mobilné zariadenia
		desc_size = 20  # Zmenšené z 22 pre lepšie zalamovanie
	elif screen_width <= 768:  # Tablety
		desc_size = 23  # Zmenšené z 25

	if FontLoader:
		FontLoader.apply_font_style(chapter_description, "character_dialogue", desc_size)

func start_chapter_intro(chapter_number: int):
	"""Spustí intro pre danú kapitolu"""
	current_chapter = chapter_number
	print("🎬 Spúšťam intro pre kapitolu ", chapter_number)

	if not chapter_data.has(chapter_number):
		print("❌ Chyba: Neexistujúce dáta pre kapitolu ", chapter_number)
		_finish_intro()
		return

	var data = chapter_data[chapter_number]

	# Nastaví titulok
	chapter_title.text = data.title

	# Načíta background image pre kapitolu
	_load_background_image(chapter_number)

	# Načíta a spustí audio pre úvodné video
	_load_and_play_intro_audio(chapter_number)

	# Načíta a okamžite spustí sprite animáciu
	_load_and_start_sprite_animation(chapter_number, data.sprites_path)

	# Pre kapitolu 1 - prehrať blesk sekundu po spustení videa
	if chapter_number == 1:
		_schedule_lightning_sound()

	# Spustí text animáciu
	_start_intro_animation(data.description)

func _start_intro_animation(description_text: String):
	"""Spustí animáciu intro s plynulými fade efektmi"""
	# Nastaví text popisu
	chapter_description.text = description_text

	# Vytvor hlavný tween pre sekvenčné animácie
	var main_tween = create_tween()
	main_tween.set_ease(Tween.EASE_IN_OUT)
	main_tween.set_trans(Tween.TRANS_CUBIC)

	# 1. Krátka pauza pred zobrazením titulku
	main_tween.tween_interval(0.8)

	# 2. Zobraz titulok s plynulým fade in (1.2s)
	main_tween.tween_property(chapter_title, "modulate:a", 1.0, 1.2)

	# 3. Krátka pauza medzi titulkom a popisom
	main_tween.tween_interval(0.5)

	# 4. Zobraz popis s plynulým fade in (1.0s)
	main_tween.tween_property(chapter_description, "modulate:a", 1.0, 1.0)



func _load_and_start_sprite_animation(chapter_number: int, sprites_path: String):
	"""Načíta sprite-y a vytvorí animáciu pre kapitolu"""
	print("🎬 Načítavam sprite animáciu pre kapitolu ", chapter_number)

	# Vytvorí SpriteFrames resource
	var sprite_frames = SpriteFrames.new()
	sprite_frames.add_animation("chapter_intro")

	# Načíta všetky frame-y (151 frame-ov pre každú kapitolu)
	var loaded_frames = 0
	for i in range(1, 152):  # frame_0001.png až frame_0151.png
		var frame_path = sprites_path + "frame_%04d.png" % i

		# Skús načítať textúru priamo
		var texture = null
		if ResourceLoader.exists(frame_path):
			texture = load(frame_path)
		else:
			# Ak neexistuje .import súbor, skús načítať priamo zo súborového systému
			var file_path = frame_path.replace("res://", "")
			if FileAccess.file_exists(file_path):
				var image = Image.new()
				var error = image.load(file_path)
				if error == OK:
					texture = ImageTexture.new()
					texture.set_image(image)

		if texture:
			sprite_frames.add_frame("chapter_intro", texture)
			loaded_frames += 1
		else:
			print("❌ Chyba načítania frame: ", frame_path)

	print("📊 Načítané frame-y: ", loaded_frames, "/151")

	if loaded_frames == 0:
		print("❌ Žiadne frame-y neboli načítané! Používam fallback.")
		_show_static_background()
		return

	# Nastaví sprite frames a okamžite spustí animáciu
	animated_sprite.sprite_frames = sprite_frames
	animated_sprite.animation = "chapter_intro"
	animated_sprite.visible = true  # Zobrazí okamžite

	# Nastaví FPS na 30 (rovnaké ako originálne video)
	sprite_frames.set_animation_speed("chapter_intro", 30.0)

	# Spustí animáciu okamžite
	animated_sprite.play("chapter_intro")

	# Pripojí signál pre koniec animácie
	if not animated_sprite.animation_finished.is_connected(_on_animation_finished):
		animated_sprite.animation_finished.connect(_on_animation_finished)

	# Backup timer pre prípad, že signál nefunguje
	_start_backup_timer()

	print("✅ Sprite animácia spustená pre kapitolu ", chapter_number)



func _start_backup_timer():
	"""Backup timer pre prípad, že animation_finished signál nefunguje"""
	# 151 frame-ov pri 30 FPS = 5.03 sekundy (bez dodatočného čakania)
	await get_tree().create_timer(5.5).timeout

	# Ak sa intro ešte neskončilo, skončí ho
	if is_inside_tree():
		print("⏰ Backup timer - končím intro")
		_finish_intro_with_crossfade()

func _show_static_background():
	"""Fallback ak sa sprite-y nenačítajú - okamžite prejde na kapitolu"""
	animated_sprite.visible = false

	# Okamžite dokončiť intro bez statickej obrazovky
	print("🎬 Sprite-y sa nenačítali, okamžite končím intro")
	_finish_intro_with_crossfade()

func _on_animation_finished():
	"""Callback po dokončení sprite animácie - okamžite prejde na kapitolu bez statickej obrazovky"""
	print("🎬 Sprite animácia dokončená, okamžite končím intro")

	# Skryje animáciu - NEZOBRAZIŤ background image (statickú obrazovku)
	animated_sprite.visible = false

	# Okamžite dokončiť intro bez čakania
	print("🎬 Okamžite začínam crossfade do kapitoly hudby")
	_finish_intro_with_crossfade()

func _load_background_image(chapter_number: int):
	"""Načíta background image pre kapitolu"""
	if not chapter_backgrounds.has(chapter_number):
		print("❌ Chyba: Neexistuje background pre kapitolu ", chapter_number)
		return

	var bg_path = chapter_backgrounds[chapter_number]
	print("🖼️ Načítavam background: ", bg_path)

	var bg_texture = load(bg_path)
	if bg_texture and background_image:
		background_image.texture = bg_texture
		print("✅ Background načítaný: ", bg_path)
	else:
		print("❌ Chyba načítania background: ", bg_path)

func _load_and_play_intro_audio(chapter_number: int):
	"""Načíta a spustí audio pre úvodné video kapitoly"""
	var audio_path = "res://audio/kapitoly_uvody_zvuky/" + str(chapter_number) + ".mp3"
	print("🎵 Načítavam intro audio: ", audio_path)

	var audio_stream = load(audio_path)
	if audio_stream:
		audio_player.stream = audio_stream
		audio_player.volume_db = -5.0  # Mierne znížená hlasitosť
		audio_player.play()
		print("✅ Intro audio spustené: ", audio_path)
	else:
		print("❌ Chyba načítania intro audia: ", audio_path)

func _schedule_lightning_sound():
	"""Naplánuje prehranie blesku sekundu po spustení videa"""
	print("⚡ Plánovanie blesku pre kapitolu 1 za 1 sekundu")
	await get_tree().create_timer(1.0).timeout

	if AudioManager and is_instance_valid(AudioManager):
		AudioManager.play_lightning_sound()
		print("⚡ Blesk prehrávam!")

func _finish_intro_with_crossfade():
	"""Dokončí intro s crossfade audio a prejde na kapitolu"""
	print("🎬 Dokončujem intro pre kapitolu ", current_chapter, " s crossfade")

	# Spustí kapitolu hudbu v tichosti
	_start_chapter_music_silent()

	# Vytvor crossfade efekt
	var fade_tween = create_tween()
	fade_tween.set_ease(Tween.EASE_IN_OUT)
	fade_tween.set_trans(Tween.TRANS_CUBIC)

	# Fade out text (0.8s)
	fade_tween.parallel().tween_property(chapter_title, "modulate:a", 0.0, 0.8)
	fade_tween.parallel().tween_property(chapter_description, "modulate:a", 0.0, 0.8)

	# Crossfade audio: intro audio fade out a kapitola hudba fade in (2.0s)
	if audio_player.playing:
		fade_tween.parallel().tween_property(audio_player, "volume_db", -40.0, 2.0)

	# Fade in kapitola hudba
	if AudioManager and is_instance_valid(AudioManager):
		fade_tween.parallel().tween_method(_crossfade_chapter_music, 0.0, 1.0, 2.0)

	# Krátka pauza pred prechodom
	fade_tween.tween_interval(0.3)

	await fade_tween.finished

	# Krátky fade out pred prechodom na kapitolu
	await _fade_out_to_chapter()

	# Zastaví intro audio
	if audio_player.playing:
		audio_player.stop()

	# Prejdi na kapitolu
	_load_chapter()

func _finish_intro():
	"""Fallback funkcia pre prípad núdzového ukončenia"""
	_finish_intro_with_crossfade()

func _load_chapter():
	"""Načíta príslušnú kapitolu"""
	print("📖 Načítavam kapitolu ", current_chapter)

	# Nastaví GameManager
	if GameManager and is_instance_valid(GameManager):
		GameManager.current_chapter = current_chapter
		GameManager.story_phase = 0
		print("✅ GameManager nastavený: chapter=", current_chapter, ", phase=0")
	else:
		print("❌ GameManager nedostupný!")

	# Hudba už beží cez crossfade, netreba ju spúšťať znova

	# Načíta scénu kapitoly
	var chapter_scene = "res://scenes/Chapter" + str(current_chapter) + ".tscn"
	print("🎬 Pokúšam sa načítať scénu: ", chapter_scene)

	if ResourceLoader.exists(chapter_scene):
		print("✅ Scéna existuje, načítavam...")
		get_tree().change_scene_to_file(chapter_scene)
	else:
		print("❌ Chyba: Scéna kapitoly neexistuje: ", chapter_scene)
		# Fallback na generickú Chapter scénu
		print("🔄 Fallback na generickú Chapter scénu")
		get_tree().change_scene_to_file("res://scenes/Chapter.tscn")

func _start_chapter_music_silent():
	"""Spustí hudbu pre kapitolu v tichosti (pre crossfade)"""
	if not AudioManager or not is_instance_valid(AudioManager):
		print("❌ AudioManager nedostupný")
		return

	print("🎵 Spúšťam hudbu pre kapitolu ", current_chapter, " v tichosti")

	# Najprv zastaviť všetky audio efekty z predchádzajúcich kapitol
	AudioManager.stop_all_chapter_audio()

	# Spustí príslušnú hudbu pre kapitolu v tichosti
	match current_chapter:
		1:
			AudioManager.start_chapter_1()
		2:
			AudioManager.start_chapter_2()
		3:
			AudioManager.start_chapter_3()
		4:
			AudioManager.start_chapter_4()
		5:
			AudioManager.start_chapter_5()
		6:
			AudioManager.start_chapter_6()
		7:
			AudioManager.start_chapter_7()
		_:
			print("⚠️ Neznáma kapitola, používam library_secrets")
			AudioManager.play_music("library_secrets")

	# Nastaví hlasitosť na 0 pre crossfade
	if AudioManager.background_music:
		AudioManager.background_music.volume_db = -40.0

func _crossfade_chapter_music(progress: float):
	"""Postupne zvyšuje hlasitosť kapitoly hudby počas crossfade"""
	if AudioManager and is_instance_valid(AudioManager) and AudioManager.background_music:
		# Interpoluje od -40dB do normálnej hlasitosti
		var target_volume_linear = AudioManager.get_music_volume()  # Normálna hlasitosť (0.0-1.0)
		var target_volume_db = linear_to_db(target_volume_linear)  # Konverzia na dB
		var current_volume = lerp(-40.0, target_volume_db, progress)
		AudioManager.background_music.volume_db = current_volume

func _start_chapter_music():
	"""Spustí hudbu pre kapitolu (už nie je potrebné, používa sa crossfade)"""
	# Táto funkcia už nie je potrebná, hudba sa spúšťa cez crossfade
	pass

func _fade_out_to_chapter():
	"""Krátky fade out pred prechodom na kapitolu"""
	fade_overlay.visible = true
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 0.2)
	await tween.finished

func _input(event):
	"""Umožní preskočiť intro stlačením klávesy"""
	if event.is_pressed() and (event is InputEventKey or event is InputEventMouseButton):
		print("⏭️ Preskakujem intro")
		# Zastaví intro audio okamžite
		if audio_player.playing:
			audio_player.stop()
		# Zastaví sprite animáciu
		if animated_sprite.is_playing():
			animated_sprite.stop()
		_finish_intro()
